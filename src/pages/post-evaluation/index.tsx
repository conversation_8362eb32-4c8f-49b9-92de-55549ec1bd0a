import { useMutation, useQuery } from '@tanstack/react-query'
import { Link } from '@tanstack/react-router'
import {
  <PERSON><PERSON>,
  Card,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Popconfirm,
  Select,
  Table,
  type TableColumnsType,
  Tag,
  Typography,
} from 'antd'
import dayjs from 'dayjs'
import { CalendarClockIcon, FilterIcon } from 'lucide-react'
import numeral from 'numeral'
import { useState } from 'react'

import { FormItemPrefix } from '@/components/FormItemPrefix'
import { useApp } from '@/contexts/app'
import { useAuth } from '@/contexts/auth'
import { type APIResponse, request } from '@/lib/request.ts'
import { flattenAndFind, getApprovalStatusColor } from '@/universal/basic-form'
import {
  APPROVAL_STATUS,
  INDUSTRY_NEW_TYPE,
  INDUSTRY_TYPE,
  PROJECT_CATEGORY,
} from '@/universal/basic-form/constants.ts'
import { ReportModal } from '@/universal/basic-form/Report'
import type { PostEvaluationDTO } from '@/universal/basic-form/types.ts'
import { RejectModal } from '@/universal/Reject'

export function PostEvaluationIndexPage() {
  const { user } = useAuth()
  const { date } = useApp()

  const [form] = Form.useForm()

  const [filters, setFilters] = useState({})
  const [pagination, setPagination] = useState({ page_num: 1, page_size: 10 })

  const [reportOpen, setReportOpen] = useState(false)
  const [selectedRows, setSelectedRows] = useState<PostEvaluationDTO[]>([])

  const [rejectOpen, setRejectOpen] = useState(false)
  const [rejectKey, setRejectKey] = useState('')

  const getTableData = useQuery({
    queryKey: [pagination, filters],
    queryFn: async ({ queryKey: [pagination, filters] }) => {
      const response = await request<
        APIResponse<{
          Data: PostEvaluationDTO[]
          Total: number
        }>
      >('/post-evaluation/list', {
        query: { use_total: true, ...pagination, ...filters },
      })
      if (response.code !== 200001) {
        message.error(response.message)
        return null
      }
      return response.data
    },
    staleTime: 0,
    retry: false,
  })

  const getTotalPlannedInvestment = useQuery({
    queryKey: [date],
    queryFn: async ({ queryKey: [date] }) => {
      const response = await request<APIResponse<{ total: number }>>(
        '/post-evaluation/current-total',
        {
          query: { year: dayjs(date).format('YYYY') },
        },
      )
      if (response.code !== 200001) {
        message.error(response.message)
        return null
      }
      return response.data.total
    },
    staleTime: 0,
  })

  const handleDelete = useMutation({
    mutationFn: async (id: string | undefined) => {
      const res = await request<APIResponse<null>>('/post-evaluation', {
        method: 'DELETE',
        body: {
          project_ids: id ? [id] : selectedRows.map((row) => row.project_id),
        },
      })
      if (res.code === 200001) {
        message.success('操作成功')
        getTableData.refetch()
        return
      }
      message.error(res?.message)
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  const columns: TableColumnsType<PostEvaluationDTO> = [
    {
      title: '序号',
      align: 'center',
      render: (_, __, index) => {
        return index + 1
      },
      minWidth: 50,
    },
    {
      title: '编制单位',
      dataIndex: 'company_name',
      render: (value) => (
        <Typography.Text ellipsis={{ tooltip: value }}>{value}</Typography.Text>
      ),
      minWidth: 200,
    },
    {
      title: '项目名称',
      dataIndex: 'project_name',
      render: (value) => (
        <Typography.Text ellipsis={{ tooltip: value }}>{value}</Typography.Text>
      ),
      minWidth: 100,
    },
    {
      title: '国资/股权',
      dataIndex: 'project_style',
      minWidth: 100,
      render: (value) => {
        return value === 1 ? '固定资产投资' : '股权投资'
      },
    },
    {
      title: '境内/境外',
      dataIndex: 'region',
      minWidth: 100,
      render: (value) => {
        return value === 1 ? '境内' : '境外'
      },
    },
    {
      title: '省、自治区、直辖市或国家（地区）',
      dataIndex: 'project_area',
      minWidth: 250,
    },
    {
      title: '项目分类',
      dataIndex: 'project_category',
      minWidth: 100,
      render: (value) => {
        return value
          ?.split(',')
          .map(
            (item: string) =>
              PROJECT_CATEGORY.find((i) => i.value === item)?.label + ',',
          )
      },
    },
    {
      title: '主业/非主业',
      dataIndex: 'is_major',
      minWidth: 100,
      render: (value) => {
        return value === 1 ? '主业' : '非主业'
      },
    },
    {
      title: '所属行业',
      dataIndex: 'industry_type',
      minWidth: 100,
      render: (value) => {
        return INDUSTRY_TYPE.find((item) => item.value === value)?.label
      },
    },
    {
      title: '所属战新产业',
      dataIndex: 'industry_new_type',
      minWidth: 100,
      render: (value) => {
        return flattenAndFind(INDUSTRY_NEW_TYPE, value)?.label
      },
    },
    {
      title: '项目总投资（万元）',
      dataIndex: 'project_total_investment',
      align: 'right',
      render: (value) => (
        <Typography.Text ellipsis={{ tooltip: value }}>
          {numeral(value).format('0,0.00')}
        </Typography.Text>
      ),
      minWidth: 200,
    },
    {
      title: '项目开始时间',
      dataIndex: 'start_time',
      render: (text) => dayjs(text).format('YYYY-MM-DD'),
      minWidth: 100,
    },
    {
      title: '项目完成或预计完成时间',
      dataIndex: 'complete_time_expect',
      render: (text) => dayjs(text).format('YYYY-MM-DD'),
      minWidth: 200,
    },
    {
      title: '组织形式',
      dataIndex: 'organization_type',
      minWidth: 100,
      render: (value) => {
        return value === 1 ? '集团组织' : '子企业组织'
      },
    },
    {
      title: '评价方式',
      dataIndex: 'eval_type',
      minWidth: 100,
      render: (value) => {
        return value === 1 ? '企业自评' : '第三方评价'
      },
    },
    {
      title: '状态',
      dataIndex: 'approval_node_status',
      minWidth: 100,
      render: (value) => {
        return (
          <Tag color={getApprovalStatusColor(value)}>
            {APPROVAL_STATUS.find((item) => item.value === value)?.label}
          </Tag>
        )
      },
    },
    {
      title: '最新上报月份',
      dataIndex: 'reprot_month',
      minWidth: 100,
      render: (value) =>
        dayjs(value).format('YYYY-MM') === '1901-01'
          ? ''
          : dayjs(value).format('YYYY-MM'),
    },
    {
      title: '创建人',
      dataIndex: 'modify_name',
      minWidth: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      minWidth: 100,
    },
    {
      title: '操作',
      fixed: 'right',
      render: (_, record) => {
        return (
          <>
            <Link
              to="/basic-report/post-evaluation/$id/update"
              params={{ id: record.approval_node_id }}
            >
              <Button type="link" size="small">
                编辑
              </Button>
            </Link>
            <Divider type="vertical" />
            <Button type="link" size="small">
              修改记录
            </Button>
            <Divider type="vertical" />
            {record.company_id !== user?.company_id && (
              <>
                <Button
                  type="link"
                  onClick={() => {
                    setRejectKey(record.approval_node_id)
                    setRejectOpen(true)
                  }}
                  size="small"
                >
                  <span className="text-[#CC8B07]">驳回</span>
                </Button>
                <Divider type="vertical" />
              </>
            )}

            <Popconfirm
              title="确认删除？"
              okText="确认"
              cancelText="取消"
              onConfirm={() => handleDelete.mutate(record.project_id)}
            >
              <Button type="link" danger size="small">
                删除
              </Button>
            </Popconfirm>
          </>
        )
      },
    },
  ]

  return (
    <div className="flex h-full flex-col gap-4">
      <ReportModal
        open={reportOpen}
        setOpen={setReportOpen}
        selectedKeys={selectedRows.map((row) => row.approval_node_id)}
        reportUrl="/fixed-assets/pending-mul"
        reportMonths={selectedRows.map((row) => row.reprot_month)}
      />

      <RejectModal
        open={rejectOpen}
        setOpen={setRejectOpen}
        rejectKey={rejectKey}
        rejectUrl="/fixed-assets/reject"
      />

      <Card>
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">{user?.company}</h2>
          <div className="flex items-center gap-2">
            <CalendarClockIcon className="size-4" />
            <span className="text-sm text-[#666]">本年度项目计划总投资：</span>
            <span className="text-xl font-semibold">
              {numeral(getTotalPlannedInvestment.data).format('0,0.00')}万元
            </span>
          </div>
        </div>
      </Card>
      <Card>
        <div className="flex flex-col gap-4">
          <Form
            form={form}
            onFinish={(values) => {
              const { date, ...data } = values
              const [start_time, end_time] = date || []

              setFilters({
                ...data,
                start_time:
                  start_time && dayjs(start_time).format('YYYY-MM-DD HH:mm:ss'),
                end_time:
                  end_time && dayjs(end_time).format('YYYY-MM-DD HH:mm:ss'),
              })
            }}
            onReset={() => {
              setFilters({})
              setPagination({ page_num: 1, page_size: 10 })
            }}
          >
            <div className="flex items-end gap-2">
              <div className="grid flex-1 grid-cols-3 gap-4 2xl:grid-cols-6">
                <Form.Item className="!mb-0" name="project_name">
                  <Input
                    className="w-full"
                    placeholder="请输入项目名称"
                    prefix={<FormItemPrefix title="项目名称" />}
                  />
                </Form.Item>
                <Form.Item className="!mb-0" name="project_category">
                  <Select
                    className="w-full"
                    placeholder="请选择项目分类"
                    prefix={<FormItemPrefix title="项目分类" />}
                    options={PROJECT_CATEGORY}
                    showSearch
                  />
                </Form.Item>
                <Form.Item className="!mb-0" name="industry_type">
                  <Select
                    className="w-full"
                    placeholder="请选择所属行业"
                    prefix={<FormItemPrefix title="所属行业" />}
                    options={INDUSTRY_TYPE}
                    showSearch
                    optionFilterProp="label"
                  />
                </Form.Item>
                <Form.Item className="!mb-0" name="approval_status">
                  <Select
                    className="w-full"
                    placeholder="请选择状态"
                    prefix={<FormItemPrefix title="状态" />}
                    options={APPROVAL_STATUS}
                  />
                </Form.Item>
                <Form.Item className="!mb-0" name="date">
                  <DatePicker.RangePicker
                    className="w-full"
                    prefix={<FormItemPrefix title="项目起始时间" />}
                  />
                </Form.Item>
              </div>
              <div className="flex shrink-0 grow-0 items-center gap-2">
                <Button
                  type="default"
                  icon={<FilterIcon className="size-3.5" />}
                />
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={getTableData.isLoading}
                >
                  搜索
                </Button>
                <Button
                  type="text"
                  htmlType="reset"
                  loading={getTableData.isLoading}
                >
                  清空
                </Button>
              </div>
            </div>
          </Form>
          <div className="flex items-center justify-end gap-2">
            <Link to="/basic-report/post-evaluation/create">
              <Button type="primary">新建数据</Button>
            </Link>
            <Button
              onClick={() => setReportOpen(true)}
              disabled={selectedRows.length < 1}
            >
              上报数据
            </Button>
            <Button>导入数据</Button>
            <Button>导出数据</Button>
            <Popconfirm
              title="确认删除所选项？"
              okText="确认"
              cancelText="取消"
              onConfirm={() => handleDelete.mutate('')}
            >
              <Button danger disabled={selectedRows.length < 1}>
                批量删除
              </Button>
            </Popconfirm>
          </div>
          <Table
            loading={
              getTableData.isFetching || getTotalPlannedInvestment.isFetching
            }
            size="small"
            rowSelection={{
              type: 'checkbox',
              columnWidth: 40,
              align: 'center',
              onChange(_, selectedRows) {
                setSelectedRows(selectedRows)
              },
            }}
            dataSource={getTableData.data?.Data}
            columns={columns}
            scroll={{ x: 'max-content' }}
            sticky={{ offsetHeader: 48 }}
            pagination={{
              showQuickJumper: true,
              showSizeChanger: true,
              total: getTableData.data?.Total,
              onChange: (page, pageSize) => {
                setPagination({ page_num: page, page_size: pageSize })
              },
            }}
            rowKey="approval_node_id"
          />
        </div>
      </Card>
    </div>
  )
}
