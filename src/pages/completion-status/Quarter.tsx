import { Link, usePara<PERSON>, useRouter } from '@tanstack/react-router'
import { <PERSON>ton, Card, Table, type TableColumnsType } from 'antd'
import { CalendarClockIcon } from 'lucide-react'
import numeral from 'numeral'
import { useMemo } from 'react'

import SkeletonTable, {
  type SkeletonTableColumnsType,
} from '@/components/SkeletonTable'
import { useAuth } from '@/contexts/auth'
import { extractYearAndQuarter } from '@/lib/dateUtils'

import { CompletionStatusTableNamesMap } from './constants'

export function QuarterIndexPage() {
  const router = useRouter()
  const { user } = useAuth()
  const { quarter } = useParams({ strict: false })
  const { year, quarter: quarterNumber } = extractYearAndQuarter(quarter || '')
  const tableData = useMemo(() => {
    return Object.entries(CompletionStatusTableNamesMap).map(
      ([key, value]) => ({
        label: value,
        value: key,
      }),
    )
  }, [])

  const columns = useMemo(() => {
    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '表单名称',
        dataIndex: 'label',
        minWidth: 160,
        render: (label, record) => (
          <Link
            to="/data-summary/completion-status/$quarter/$table/list"
            params={{ quarter: quarter || '', table: record.value }}
          >
            {label}
          </Link>
        ),
      },
    ] as TableColumnsType<(typeof tableData)[number]>
  }, [quarter])

  return (
    <div className="flex h-full flex-col gap-4">
      <Button
        className="-my-4 self-start"
        type="link"
        onClick={() => router.history.back()}
      >
        {'<< 返回上一页'}
      </Button>
      <Card>
        <div className="space-y-6">
          <div className="flex justify-between">
            <h2 className="text-xl font-semibold">{user?.company}</h2>
            <h3 className="text-lg font-semibold">
              {year}年第{quarterNumber}季度
            </h3>
          </div>
          <div className="flex items-center gap-2">
            <CalendarClockIcon className="size-4" />
            <span className="text-sm text-[#666]">本年度计划投资总额：</span>
            <span className="text-xl font-semibold">
              {numeral(73472163.213).format('0,0.00')}万元
            </span>
          </div>
        </div>
      </Card>
      <Card title="投资计划表" extra={<Button>导出数据</Button>}>
        <SkeletonTable columns={columns as SkeletonTableColumnsType[]}>
          <Table
            rowKey="value"
            size="small"
            pagination={false}
            dataSource={tableData}
            columns={columns}
            scroll={{ x: 'max-content' }}
            sticky={{ offsetHeader: 48 }}
          />
        </SkeletonTable>
      </Card>
    </div>
  )
}
