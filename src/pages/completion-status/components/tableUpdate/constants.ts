// 投资计划总表数据类型标识
export const DATA_TYPE_INVESTMENT_PLAN = {
  '1': '一、固定资产投资',
  '2': '（一）固定资产投资（不含房地产）',
  '3': '（二）房地产投资',
  '4': '二、股权投资',
  '5': '（一）对外并购',
  '6': '（二）对外参股',
  '7': '（三）新设',
  '8': '（四）增资（增持）',
  '9': '（五）其他股权投资',
  '10': '三、合计',
}

// 投资计划禁用单元格
export const DISABLED_CELL_STYLE = [1, 4, 10]
// 投资计划新开和续建禁用的单元格
export const DISABLED_COMMENCEMENT_CONTINUATION_STYLE = [4, 5, 6, 7, 8, 9, 10]

//战新表数据类型标识
export const DATA_TYPE_STRATEGIC_NEW = {
  '0100': '一、新一代信息技术产业',
  '0101': '其中：新一代移动通信',
  '0102': '集成电路（含设计、制造、封测、装备、材料）',
  '0103': '工业软件和技术服务',
  '0104': '人工智能',
  '0105': '工业互联网',
  '0106': '云计算与大数据',
  '0107': '新型电子器件',
  '0108': '材料与设备制造（不含集成电路）',
  '0199': '其他',
  '0200': '二、高端装备制造产业（如涉及，请按备注细分）',
  '0201': '其中：智能制造装备',
  '0299': '其他',
  '0300': '三、新材料产业',
  '0301': '其中：先进钢铁材料',
  '0302': '先进有色金属材料',
  '0303': '先进化工新材料',
  '0304': '先进无机非金属材料',
  '0305': '高性能纤维及复合材料',
  '0399': '其他',
  '0400': '四、生物产业（如涉及，请按备注细分）',
  '0401': '其中：生物医药',
  '0402': '生物制造',
  '0499': '其他',
  '0500': '五、新能源汽车产业（如涉及，请按备注细分）',
  '0600': '六、新能源产业',
  '0601': '其中：核电',
  '0602': '风能',
  '0603': '太阳能',
  '0604': '生物质能',
  '0605': '氢能',
  '0606': '智能电网',
  '0607': '储能',
  '0699': '其他',
  '0700': '七、节能环保产业',
  '0800': '八、字创意产业',
  '0900': '九、相关服务业',
}

// 战新表禁用单元格
export const DISABLED_CELL_STRATEGIC_NEW = [
  '0100',
  '0200',
  '0300',
  '0400',
  '0600',
]

//行业设公式数据类型
export const DATA_TYPE_INDUSTRY_SET_FORMULA = {
  '0100': '合计',
  '0101': '制糖业',
  '0102': '专用化学产品制造',
  '0103': '炸药、火工及焰火产品制造',
  '0104': '物流搬运设备制造业',
  '0105': '医疗仪器设备及器械制造',
  '0106': '通信设备制造',
  '0107': '电子元件及电子专用材料制造',
  '0108': '其他房屋建筑业',
  '0109': '铁路、道路、隧道和桥梁工程建筑',
  '0110': '电力工程施工',
  '0111': '其他土木工程建筑',
}

// 行业设公式禁用单元格
export const DISABLED_CELL_INDUSTRY_SET_FORMULA = ['0100']

// 区域数据类型
export const DATA_TYPE_AREA = {
  '0100': '合计',
  '0101': '北京',
  '0102': '天津',
  '0103': '河北',
  '0104': '其中：雄安新区',
  '0105': '山西',
  '0106': '内蒙古',
  '0107': '辽宁',
  '0108': '吉林',
  '0109': '黑龙江',
  '0110': '上海',
  '0111': '江苏',
  '0112': '浙江',
  '0113': '安徽',
  '0114': '福建',
  '0115': '江西',
  '0116': '山东',
  '0117': '河南',
  '0118': '湖北',
  '0119': '湖南',
  '0120': '其中：珠三角九市',
  '0121': '其中：横琴新区',
  '0122': '广西',
  '0123': '海南',
  '0124': '重庆',
  '0125': '四川',
  '0126': '贵州',
  '0127': '云南',
  '0128': '西藏',
  '0129': '陕西',
  '0130': '甘肃',
  '0131': '青海',
  '0132': '宁夏',
  '0133': '新疆',
  '0134': '香港',
  '0135': '澳门',
  '0136': '台湾',
  '0137': '跨区域投资',
}

// 区域禁用单元格
export const DISABLED_CELL_AREA = ['0100']
// 其中区域
export const INCLUDE_AREA = ['0104', '0120', '0121']
// 重点区域
export const IMPORTANT_AREA = ['0134', '0135', '0136']
