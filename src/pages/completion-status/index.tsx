import { Link } from '@tanstack/react-router'
import { <PERSON>ton, Card, Table, type TableColumnsType } from 'antd'
import { CalendarClockIcon } from 'lucide-react'
import numeral from 'numeral'
import { useMemo } from 'react'

import SkeletonTable, {
  type SkeletonTableColumnsType,
} from '@/components/SkeletonTable'
import { useApp } from '@/contexts/app'
import { useAuth } from '@/contexts/auth'

export function CompletionStatusIndexPage() {
  const { user } = useAuth()
  const { date } = useApp()
  const tableData = useMemo(() => {
    return Array.from({ length: 4 }, (_, index) => ({
      label: `${date.year()}-Q${index + 1}`,
      value: `${date.year()}-Q${index + 1}`,
    }))
  }, [date])

  const columns = useMemo(() => {
    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '填报周期',
        dataIndex: 'label',
        minWidth: 160,
        render: (label, record) => (
          <Link
            to="/data-summary/completion-status/$quarter"
            params={{ quarter: record.value }}
          >
            {label}
          </Link>
        ),
      },
    ] as TableColumnsType<(typeof tableData)[number]>
  }, [])

  return (
    <div className="flex h-full flex-col gap-4">
      <Card>
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">{user?.company}</h2>
          <div className="flex items-center gap-2">
            <CalendarClockIcon className="size-4" />
            <span className="text-sm text-[#666]">本年度计划投资总额：</span>
            <span className="text-xl font-semibold">
              {numeral(73472163.213).format('0,0.00')}万元
            </span>
          </div>
        </div>
      </Card>
      <Card title="投资计划表" extra={<Button>导出数据</Button>}>
        <SkeletonTable columns={columns as SkeletonTableColumnsType[]}>
          <Table
            rowKey="value"
            size="small"
            pagination={false}
            dataSource={tableData}
            columns={columns}
            scroll={{ x: 'max-content' }}
            sticky={{ offsetHeader: 48 }}
          />
        </SkeletonTable>
      </Card>
    </div>
  )
}
