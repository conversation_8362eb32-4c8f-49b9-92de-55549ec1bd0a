import { CompletionTotalTable } from './components/table/CompletionTotalTable'
import { CompletionTotalUpdate } from './components/tableUpdate/CompletionTotalUpdate'

/** 完成状况相关表名 */
export const CompletionStatusTable = {
  /** 完成总表 */
  CompletionTotal: 'completion-total',
  /** 战新*/
  StrategicNew: 'strategic-new',
  /** 行业设公式 */
  IndustrySetFormula: 'industry-set-formula',
  /** 区域 */
  Area: 'area',
  /** 固资项目 */
  FixedAssetProject: 'fixed-asset-project',
  /** 股权项目 */
  EquityProject: 'equity-project',
  /** 后评价 */
  PostEvaluation: 'post-evaluation',
  /** 补充附件 */
  SupplementAttachment: 'supplement-attachment',
} as const

/** 投资完成状况相关表名 */
export const CompletionStatusTableNamesMap = {
  [CompletionStatusTable.CompletionTotal]: '完成总表',
  [CompletionStatusTable.StrategicNew]: '战新',
  [CompletionStatusTable.IndustrySetFormula]: '行业设公式',
  [CompletionStatusTable.Area]: '区域',
  [CompletionStatusTable.FixedAssetProject]: '固资项目',
  [CompletionStatusTable.EquityProject]: '股权项目',
  [CompletionStatusTable.PostEvaluation]: '后评价',
  [CompletionStatusTable.SupplementAttachment]: '补充附件',
}

/** 投资完成状况页面对应组件 */
export const CompletionStatusTableMap = {
  [CompletionStatusTable.CompletionTotal]: CompletionTotalTable,
  // [CompletionStatusTable.StrategicNew]: StrategicNewTable,
  // [CompletionStatusTable.IndustrySetFormula]: IndustrySetFormulaTable,
  // [CompletionStatusTable.Area]: AreaTable,
  // [CompletionStatusTable.FixedAssetProject]: FixedAssetProjectTable,
  // [CompletionStatusTable.EquityProject]: EquityProjectTable,
  // [CompletionStatusTable.PostEvaluation]: PostEvaluationTable,
  // [CompletionStatusTable.SupplementAttachment]: SupplementAttachmentTable,
} as const

/** 投资完成状况页面对应组件 */
export const CompletionStatusTableUpdateMap = {
  [CompletionStatusTable.CompletionTotal]: CompletionTotalUpdate,
  // [CompletionStatusTable.StrategicNew]: StrategicNewTable,
  // [CompletionStatusTable.IndustrySetFormula]: IndustrySetFormulaTable,
  // [CompletionStatusTable.Area]: AreaTable,
  // [CompletionStatusTable.FixedAssetProject]: FixedAssetProjectTable,
  // [CompletionStatusTable.EquityProject]: EquityProjectTable,
  // [CompletionStatusTable.PostEvaluation]: PostEvaluationTable,
  // [CompletionStatusTable.SupplementAttachment]: SupplementAttachmentTable,
} as const
