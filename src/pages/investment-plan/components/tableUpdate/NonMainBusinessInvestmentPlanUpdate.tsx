import { useQuery, useMutation } from '@tanstack/react-query'
import { useParams, useSearch, Link } from '@tanstack/react-router'
import {
  Card,
  message,
  Button,
  Table,
  type TableColumnsType,
  InputNumber,
} from 'antd'
import { createStyles } from 'antd-style'
import { RefreshCcw } from 'lucide-react'
import { useMemo, useState, useEffect, useCallback, memo } from 'react'

import { useAuth } from '@/contexts/auth.tsx'
import {
  toNumber,
  addNumbers,
  divideNumbers,
  subtractNumbers,
} from '@/lib/numberUtils.ts'
import { type APIResponse, request } from '@/lib/request.ts'

import { InvestmentPlanTableNamesMap } from '../../enum/InvestmentPlan.ts'

import type { NonMainBusinessInvestmentPlanCompany } from './types.ts'

// 优化后的输入组件
const NumberInput = memo(
  ({
    value,
    record,
    field,
    disabled = false,
    onChange,
  }: {
    value: string | number
    record: NonMainBusinessInvestmentPlanCompany
    field: keyof NonMainBusinessInvestmentPlanCompany
    disabled?: boolean
    onChange?: (
      record: NonMainBusinessInvestmentPlanCompany,
      field: string,
      value: number,
    ) => void
  }) => (
    <InputNumber
      value={toNumber(value)}
      onChange={(val) =>
        val !== null && onChange && onChange(record, field, val)
      }
      disabled={disabled}
      style={{ width: '100%' }}
      min={0}
      precision={2}
      controls={false}
    />
  ),
)

NumberInput.displayName = 'NumberInput'

const useStyle = createStyles(({ css }) => {
  const antCls = '.ant'
  return {
    customTable: css`
      ${antCls}-table-thead > tr > th {
        background-color: #e5ebfe;
        font-weight: 400;
        white-space: pre-wrap;
      }
    `,
  }
})

export const NonMainBusinessInvestmentPlanUpdate = () => {
  const { styles } = useStyle()
  const [messageApi, contextHolder] = message.useMessage()
  const { user } = useAuth()
  const { id } = useSearch({ strict: false }) as { id: string }
  const { tableName } = useParams({ strict: false })

  const [tableData, setTableData] = useState<
    NonMainBusinessInvestmentPlanCompany[]
  >([])
  const [summaryData, setSummaryData] = useState<{
    id: string
    investment_year: string
    company_id: string
    company_name: string
    consolidation: number
  }>({
    id: '',
    investment_year: '',
    company_id: '',
    company_name: '',
    consolidation: 1,
  })

  // 刷新数据
  const { mutate, isPending } = useMutation({
    mutationFn: async (params: { company_id: string; year: string }) => {
      const res = await request<
        APIResponse<{
          data: NonMainBusinessInvestmentPlanCompany[]
        }>
      >('/plan-summary/non-main-investment-aggregate', {
        method: 'GET',
        query: params,
      })
      return res
    },
    onSuccess: (res) => {
      if (res.code === 200001) {
        messageApi.success('刷新成功')
        const tableData = res.data?.data
        setTableData((prev) => {
          return tableData.map((v) => ({
            ...v,
            id: prev[0]?.id ?? '',
          }))
        })
      } else {
        messageApi.error(res?.message)
      }
    },
    onError: (err) => messageApi.error(JSON.stringify(err)),
  })

  // 保存数据
  const { mutate: saveMutate, isPending: saveIsPending } = useMutation({
    mutationFn: async (params: NonMainBusinessInvestmentPlanCompany) => {
      const res = await request('/plan-summary/non-main-investment-save', {
        method: 'POST',
        body: params,
      })
      return res
    },
    onSuccess: (res) => {
      if (res.code === 200001) {
        messageApi.success('保存成功')
      } else {
        messageApi.error(res?.message)
      }
    },
    onError: (err) => messageApi.error(JSON.stringify(err)),
  })

  // 获取初始数据
  const { data, isLoading } = useQuery({
    queryKey: ['/plan-summary/non-main-investment-detail-by-id', id],
    queryFn: async ({ queryKey: [url, id] }) => {
      const response = await request<
        APIResponse<{
          data: NonMainBusinessInvestmentPlanCompany
        }>
      >(url as string, { query: { id } })
      if (response.code !== 200001) return null
      return response?.data?.data ?? null
    },
    staleTime: 0,
    retry: false,
    enabled: !!id,
  })

  // 核心计算函数 - 按依赖顺序计算所有字段
  const calculateSums = useCallback(
    (data: NonMainBusinessInvestmentPlanCompany) => {
      const total = addNumbers(
        data.special1,
        data.special2,
        data.special3,
        data.special4,
      )
      const subtractData = subtractNumbers(data.non_main_total, total)
      const newData = [
        {
          ...data,
          special_total: total,
          excluded_non_main: subtractData,
          apply_non_main: divideNumbers(subtractData, data.total),
        },
      ]
      return newData
    },
    [],
  )

  // 初始化表格数据
  useEffect(() => {
    if (data) {
      // 初始化时执行一次计算
      const initialData = calculateSums(data)
      setTableData(initialData)
      setSummaryData({
        id: data.id || '',
        investment_year: data.investment_year,
        company_id: data.company_id,
        company_name: data.company_name,
        consolidation: data.consolidation,
      })
    }
  }, [data, calculateSums])

  const handleInputChange = useCallback(
    (
      record: NonMainBusinessInvestmentPlanCompany,
      field: string,
      value: number,
    ) => {
      const newData = calculateSums({ ...record, [field]: value })
      setTableData(newData)
    },
    [calculateSums],
  )

  // 处理表单提交
  const handleSubmit = useCallback(() => {
    saveMutate(tableData[0])
  }, [tableData, saveMutate])

  // 检查是否为所有报表（禁用编辑）
  const isConsolidatedReport = (
    consolidation: number | string | undefined,
  ): boolean => {
    return Number(consolidation) === 2
  }

  // 表格列配置
  const columns: TableColumnsType<NonMainBusinessInvestmentPlanCompany> =
    useMemo(() => {
      const isDisabled = isConsolidatedReport(summaryData.consolidation)
      return [
        {
          title: '计划投资总额',
          dataIndex: 'total',
          key: 'total',
          width: 150,
          align: 'center',
          fixed: 'left',
          render: (value, record) => (
            <NumberInput value={value} record={record} field="total" disabled />
          ),
        },
        {
          title: '非主业投资额',
          dataIndex: 'non_main_total',
          key: 'non_main_total',
          width: 150,
          align: 'center',
          render: (value, record) => (
            <NumberInput
              value={value}
              record={record}
              field="non_main_total"
              disabled
            />
          ),
        },
        {
          title: '符合监管要求需要在核定非主业投资比例中剔除考虑的事项',
          align: 'center',
          children: [
            {
              title: '出资国家有关部门执行落实专项任务基金的投资额',
              dataIndex: 'special1',
              key: 'special1',
              width: 150,
              align: 'center',
              render: (value, record) => (
                <NumberInput
                  value={value}
                  record={record}
                  field="special1"
                  disabled={isDisabled}
                  onChange={handleInputChange}
                />
              ),
            },
            {
              title:
                '出资经国务院国资委批准并由中央企业设立和管理的、落实国资国企改革任务基金的投资额',
              dataIndex: 'special2',
              key: 'special2',
              width: 150,
              align: 'center',
              render: (value, record) => (
                <NumberInput
                  value={value}
                  record={record}
                  field="special2"
                  disabled={isDisabled}
                  onChange={handleInputChange}
                />
              ),
            },
            {
              title: '中央企业设立和管理的投向明确且投资于主业产业基金的投资额',
              dataIndex: 'special3',
              key: 'special3',
              width: 150,
              align: 'center',
              render: (value, record) => (
                <NumberInput
                  value={value}
                  record={record}
                  field="special3"
                  disabled={isDisabled}
                  onChange={handleInputChange}
                />
              ),
            },
            {
              title: '创业投资基金',
              dataIndex: 'special4',
              key: 'special4',
              width: 150,
              align: 'center',
              render: (value, record) => (
                <NumberInput
                  value={value}
                  record={record}
                  field="special4"
                  disabled={isDisabled}
                  onChange={handleInputChange}
                />
              ),
            },
            {
              title: '合计',
              dataIndex: 'special_total',
              key: 'special_total',
              width: 150,
              align: 'center',
              render: (value, record) => (
                <NumberInput
                  value={value}
                  record={record}
                  field="special_total"
                  disabled
                  onChange={handleInputChange}
                />
              ),
            },
          ],
        },
        {
          title: '剔除后非主业投资额',
          dataIndex: 'excluded_non_main',
          key: 'excluded_non_main',
          width: 150,
          align: 'center',
          render: (value, record) => (
            <NumberInput
              value={value}
              record={record}
              field="excluded_non_main"
              disabled
              onChange={handleInputChange}
            />
          ),
        },
        {
          title: '申请的非主业投资比例',
          dataIndex: 'apply_non_main',
          key: 'apply_non_main',
          width: 150,
          align: 'center',
          render: (value, record) => (
            <NumberInput
              value={value}
              record={record}
              field="apply_non_main"
              disabled
              onChange={handleInputChange}
            />
          ),
        },
      ]
    }, [handleInputChange, summaryData.consolidation])

  const title =
    InvestmentPlanTableNamesMap[
      tableName as keyof typeof InvestmentPlanTableNamesMap
    ]
  return (
    <div className="flex h-full flex-col">
      {contextHolder}

      <Card
        title={title}
        extra={
          isConsolidatedReport(summaryData.consolidation) ? null : (
            <Button
              color="primary"
              icon={<RefreshCcw className="mt-[4px] size-4" />}
              variant="text"
              onClick={() => {
                mutate({
                  company_id: user?.company_id || '',
                  year: summaryData?.investment_year || '',
                })
              }}
            >
              刷新数据
            </Button>
          )
        }
      >
        <div className="mb-4 flex items-center justify-between">
          <p>填报年份：{summaryData?.investment_year}</p>
          <p>编制单位：{summaryData?.company_name}</p>
          <p>金额：万元</p>
        </div>
        <Table
          size="small"
          bordered
          className={styles.customTable}
          dataSource={tableData}
          columns={columns}
          loading={isLoading || isPending || saveIsPending}
          pagination={false}
          scroll={{ x: 'max-content' }}
          sticky={{ offsetHeader: 48 }}
          rowKey="company_id"
        />
        <div className="py-5">
          <p>
            注：如不涉及需要在核定非主业投资比例中剔除考虑的事项，相关指标可不填写。
          </p>
        </div>
        <div className="mt-4 flex justify-end gap-2">
          <Button type="primary" onClick={handleSubmit} loading={saveIsPending}>
            保存数据
          </Button>
          <Link
            to="/data-summary/investment-plan/table/$tableName/list"
            params={{ tableName: tableName ?? '' }}
          >
            <Button>返回</Button>
          </Link>
        </div>
      </Card>
    </div>
  )
}
