import { useQuery } from '@tanstack/react-query'
import { useRouter, useParams } from '@tanstack/react-router'
import {
  Button,
  Card,
  DatePicker,
  Form,
  Table,
  Select,
  type TableColumnsType,
  type TableProps,
  Typography,
} from 'antd'
import { createStyles } from 'antd-style'
import dayjs from 'dayjs'
import {
  CalendarClockIcon,
  BadgeJapaneseYenIcon,
  FilterIcon,
} from 'lucide-react'
import numeral from 'numeral'
import {
  parseAsBoolean,
  parseAsInteger,
  parseAsString,
  useQueryStates,
} from 'nuqs'
import { useMemo, useCallback, useEffect } from 'react'

import { FormItemPrefix } from '@/components/FormItemPrefix'
import SkeletonTable, {
  type SkeletonTableColumnsType,
} from '@/components/SkeletonTable'
import { useApp } from '@/contexts/app'
import { useAuth } from '@/contexts/auth.tsx'
import { type APIResponse, request } from '@/lib/request'
import { convertSortOrder } from '@/lib/sortUtils'

import {
  InvestmentPlanTableNamesMap,
  InvestmentPlan,
} from '../../enum/InvestmentPlan'

import type { FixedAssetProjectPageDTO } from './types'

interface SearchFormValues {
  company_id?: string // 填报单位
  date?: [dayjs.Dayjs | null, dayjs.Dayjs | null] // 日期范围
}

// 修改tabs样式
const useStyle = createStyles(({ css }) => {
  const antCls = '.ant'
  return {
    customCard: css`
      ${antCls}-card-body {
        padding-top: 0;
      }
    `,
  }
})

export function FixedAssetProjectTable() {
  const { styles } = useStyle()
  const { id } = useParams({ strict: false })
  const { user } = useAuth()
  const { date } = useApp()
  const [form] = Form.useForm()
  const [filters, setFilters] = useQueryStates({
    page_num: parseAsInteger.withDefault(1),
    page_size: parseAsInteger.withDefault(10),
    use_total: parseAsBoolean.withDefault(true),
    company_id: parseAsString.withDefault(''),
    start_time: parseAsString.withDefault(''),
    end_time: parseAsString.withDefault(''),
    sort_field: parseAsString.withDefault(''),
    sort_order: parseAsString.withDefault(''),
  })

  const formInitialValues = useMemo<SearchFormValues>(
    () => ({
      company_id: filters.company_id || '',
      date: [
        filters.start_time ? dayjs(filters.start_time) : null,
        filters.end_time ? dayjs(filters.end_time) : null,
      ],
    }),
    [filters.company_id, filters.start_time, filters.end_time],
  )

  useEffect(() => {
    form.setFieldsValue(formInitialValues)
  }, [form, formInitialValues])

  const { data: statisticalData } = useQuery({
    queryKey: [
      '/plan-summary/fixed-assets-summary',
      filters.company_id,
      date,
    ] as const,
    queryFn: async ({ queryKey: [url, company_id, date] }) => {
      const response = await request<APIResponse<FixedAssetProjectPageDTO>>(
        url as string,
        {
          query: {
            company_id,
            year: date.year(),
          },
        },
      )
      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
  })

  const { data, isLoading, isFetching } = useQuery({
    queryKey: ['/plan-summary/fixed-assets-list', filters] as const,
    queryFn: async ({ queryKey: [url, filters] }) => {
      const response = await request<
        APIResponse<{
          List: FixedAssetProjectPageDTO[]
          Total: number
        }>
      >(url as string, {
        query: {
          ...filters,
          sort_order: convertSortOrder(filters.sort_order),
          year: date.year(),
        },
      })
      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
  })

  const columns: TableColumnsType<FixedAssetProjectPageDTO> = useMemo(() => {
    const getSortOrder = (field: string): 'ascend' | 'descend' | undefined => {
      if (filters.sort_field === field) {
        if (
          filters.sort_order === 'ascend' ||
          filters.sort_order === 'descend'
        ) {
          return filters.sort_order
        }
      }
      return undefined
    }

    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '编制单位',
        dataIndex: 'company_name',
        minWidth: 160,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {value}
          </Typography.Text>
        ),
      },
      {
        title: '固定资产投资项目数量（个）',
        dataIndex: 'count',
        minWidth: 180,
        align: 'right',
        sorter: true,
        sortOrder: getSortOrder('count'),
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {numeral(value).format('0,0')}
          </Typography.Text>
        ),
      },
      {
        title: '固定资产投资计划额（万元）',
        dataIndex: 'total',
        minWidth: 180,
        align: 'right',
        sorter: true,
        sortOrder: getSortOrder('total'),
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {numeral(value).format('0,0.00')}
          </Typography.Text>
        ),
      },

      {
        title: '操作',
        width: 150,
        fixed: 'right',
        render: () => {
          return (
            <Button type="link" size="small">
              导出数据
            </Button>
          )
        },
      },
    ]
  }, [filters.sort_field, filters.sort_order])

  const handleTableChange = useCallback<
    NonNullable<TableProps<FixedAssetProjectPageDTO>['onChange']>
  >(
    (pagination, _filters, sorter) => {
      let sort_field = ''
      let sort_order = ''
      if (Array.isArray(sorter)) {
        if (sorter.length > 0) {
          sort_field = (sorter[0]?.field as string) || ''
          sort_order = sorter[0]?.order || ''
        }
      } else if (sorter) {
        sort_field = (sorter.field as string) || ''
        sort_order = sorter.order || ''
      }
      setFilters((prev) => ({
        ...prev,
        sort_field,
        sort_order,
        page_num: pagination.current,
        page_size: pagination.pageSize || prev.page_size,
      }))
    },
    [setFilters],
  )

  const onSearch = useCallback(
    (values: SearchFormValues) => {
      setFilters((prev) => ({
        ...prev,
        page_num: 1,
        start_time: values.date?.[0]?.format('YYYY-MM-DD') || '',
        end_time: values.date?.[1]?.format('YYYY-MM-DD') || '',
        company_id: values.company_id || '',
      }))
    },
    [setFilters],
  )

  const onReset = useCallback(() => {
    form.resetFields()
    setFilters((prev) => ({
      ...prev,
      page_num: 1,
      company_id: '',
      start_time: '',
      end_time: '',
      sort_field: '',
      sort_order: '',
    }))
  }, [form, setFilters])

  const router = useRouter()

  return (
    <div className="flex h-full flex-col gap-4">
      <Button
        className="-my-4 self-start"
        type="link"
        onClick={() => router.history.back()}
      >
        {'<< 返回上一页'}
      </Button>
      <Card className={styles.customCard}>
        <div className="space-y-6">
          <h2 className="mt-3 text-xl font-semibold">{user?.company}</h2>
          <div className="flex items-center gap-16">
            <div className="flex items-center gap-2">
              <CalendarClockIcon className="size-4" />
              <span className="text-sm text-[#666]">固定资产投资项目数量:</span>
              <span className="text-xl font-semibold">
                {numeral(statisticalData?.count).format('0,0')}个
              </span>
            </div>
            <div className="flex items-center gap-2">
              <BadgeJapaneseYenIcon className="size-4" />
              <span className="text-sm text-[#666]">
                固定资产项目投资计划额:
              </span>
              <span className="text-xl font-semibold">
                {numeral(statisticalData?.total).format('0,0.00')}万元
              </span>
            </div>
          </div>
        </div>
      </Card>
      <Card>
        <div className="flex flex-col gap-4">
          <Form form={form} onFinish={onSearch} onReset={onReset}>
            <div className="flex items-end gap-2">
              <div className="grid flex-1 grid-cols-3 gap-4 2xl:grid-cols-6">
                <Form.Item className="!mb-0" name="company_id">
                  <Select
                    className="w-full"
                    placeholder="请选择填报单位"
                    prefix={<FormItemPrefix title="填报单位" />}
                    options={[
                      { label: user?.company, value: user?.company_id },
                    ]}
                  />
                </Form.Item>

                <Form.Item className="!mb-0" name="date">
                  <DatePicker.RangePicker
                    className="w-full"
                    prefix={<FormItemPrefix title="创建时间" />}
                  />
                </Form.Item>
              </div>
              <div className="flex shrink-0 grow-0 items-center gap-2">
                <Button
                  type="default"
                  icon={<FilterIcon className="size-3.5" />}
                />
                <Button type="primary" htmlType="submit">
                  搜索
                </Button>
                <Button type="text" htmlType="reset">
                  清空
                </Button>
              </div>
            </div>
          </Form>
          <div className="flex items-center gap-2 text-[16px] font-[600]">
            {
              InvestmentPlanTableNamesMap[
                id as (typeof InvestmentPlan)[keyof typeof InvestmentPlan]
              ]
            }
          </div>
          <SkeletonTable
            loading={isLoading || isFetching}
            columns={columns as SkeletonTableColumnsType[]}
          >
            <Table
              size="small"
              rowSelection={{
                type: 'checkbox',
                columnWidth: 40,
                align: 'center',
              }}
              tableLayout="auto"
              dataSource={data?.List ?? []}
              columns={columns}
              scroll={{ x: 'max-content' }}
              sticky={{ offsetHeader: 48 }}
              pagination={{
                showQuickJumper: true,
                showSizeChanger: true,
                total: data?.Total,
              }}
              rowKey="id"
              onChange={handleTableChange}
            />
          </SkeletonTable>
        </div>
      </Card>
    </div>
  )
}
